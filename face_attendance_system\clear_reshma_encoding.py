#!/usr/bin/env python3
"""
Clear <PERSON><PERSON><PERSON>'s face encoding so she can be re-registered with the current system
"""

import sys
import os

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def clear_reshma_encoding():
    """Clear Reshma's face encoding"""
    print("🔧 Clear Reshma's Face Encoding")
    print("=" * 40)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma:
            print("❌ Reshma not found in database!")
            return False
        
        print(f"✅ Found Reshma (ID: {reshma.id})")
        
        if reshma.face_encoding:
            print("📊 Current encoding exists - clearing it...")
            reshma.face_encoding = None
            db.commit()
            print("✅ Face encoding cleared!")
        else:
            print("📊 No face encoding to clear")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Clear Reshma's Encoding")
    print("This will clear Reshma's face encoding so she can")
    print("be re-registered using the web interface with the")
    print("current MobileFaceNet system.")
    print()
    
    confirm = input("Clear Reshma's encoding? (y/n): ")
    if confirm.lower() == 'y':
        if clear_reshma_encoding():
            print("\n🎉 Encoding cleared successfully!")
            print()
            print("Next steps:")
            print("1. Go to: http://localhost:8000/users")
            print("2. Click 'Register Face' for Reshma")
            print("3. Capture her face using the current system")
            print("4. Test recognition again")
        else:
            print("\n❌ Failed to clear encoding!")
    else:
        print("Cancelled.")
