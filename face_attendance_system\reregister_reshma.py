#!/usr/bin/env python3
"""
Re-register Reshma with the current MobileFaceNet system
This will capture a fresh face encoding that's compatible
"""

import sys
import os
import cv2
import numpy as np

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def reregister_reshma():
    """Re-register Reshma with fresh MobileFaceNet encoding"""
    print("🔄 Re-register Reshma with MobileFaceNet")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma:
            print("❌ Reshma not found in database!")
            return False
        
        print(f"✅ Found Reshma (ID: {reshma.id})")
        
        # Check current encoding
        if reshma.face_encoding:
            current_encoding = np.frombuffer(reshma.face_encoding, dtype=np.float32)
            print(f"📊 Current encoding: {len(current_encoding)}D")
        else:
            print("📊 No current encoding")
        
        print("\n🎥 Starting webcam for face capture...")
        print("Position Reshma's face in front of the camera")
        print("Press SPACE to capture, 'q' to quit")
        
        # Open webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam!")
            return False
        
        captured_encoding = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Show frame
            cv2.imshow('Re-register Reshma', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Space to capture
                print("📸 Capturing face...")
                
                # Use current MobileFaceNet system to extract embedding
                try:
                    if face_recognition_system.use_mediapipe:
                        # Extract embedding using MediaPipe + MobileFaceNet
                        results = face_recognition_system.mediapipe_system.recognize_faces_in_frame(frame)
                        
                        if len(results) == 0:
                            print("❌ No face detected! Try again.")
                            continue
                        elif len(results) > 1:
                            print("⚠️ Multiple faces detected! Please ensure only Reshma's face is visible.")
                            continue
                        
                        # Extract the face region
                        result = results[0]
                        top, right, bottom, left = result['location']
                        face_crop = frame[top:bottom, left:right]
                        
                        if face_crop.size == 0:
                            print("❌ Invalid face crop! Try again.")
                            continue
                        
                        # Extract fresh MobileFaceNet embedding
                        embedding = face_recognition_system.mediapipe_system.extract_face_embedding_openvino(face_crop)
                        
                        if embedding is not None and len(embedding) == 128:
                            captured_encoding = embedding
                            print(f"✅ Captured fresh MobileFaceNet encoding: {len(embedding)}D")
                            print(f"   Norm: {np.linalg.norm(embedding):.3f}")
                            print(f"   Mean: {np.mean(embedding):.3f}")
                            print(f"   Std: {np.std(embedding):.3f}")
                            break
                        else:
                            print("❌ Failed to extract MobileFaceNet embedding! Try again.")
                    else:
                        print("❌ MobileFaceNet system not available!")
                        break
                        
                except Exception as e:
                    print(f"❌ Error extracting embedding: {e}")
                    continue
        
        cap.release()
        cv2.destroyAllWindows()
        
        if captured_encoding is not None:
            # Update Reshma's encoding in database
            try:
                # Convert to bytes
                encoding_bytes = captured_encoding.astype(np.float32).tobytes()
                
                # Update database
                reshma.face_encoding = encoding_bytes
                db.commit()
                
                print(f"✅ Successfully updated Reshma's face encoding!")
                print(f"   New encoding: {len(captured_encoding)}D MobileFaceNet")
                
                # Reload face recognition system
                print("🔄 Reloading face recognition system...")
                users_data = []
                for user in db.query(models.User).filter(models.User.is_active == True).all():
                    if user.face_encoding:
                        users_data.append({
                            'id': user.id,
                            'name': user.name,
                            'face_encoding': user.face_encoding
                        })
                
                face_recognition_system.load_known_faces(users_data)
                print("✅ Face recognition system reloaded!")
                
                # Test the new encoding
                print("\n🧪 Testing new encoding...")
                if len(face_recognition_system.known_names) > 0:
                    print(f"   Loaded users: {face_recognition_system.known_names}")
                    print("✅ Reshma should now be recognized correctly!")
                else:
                    print("⚠️ No users loaded - check the system")
                
                return True
                
            except Exception as e:
                print(f"❌ Error updating database: {e}")
                db.rollback()
                return False
        else:
            print("❌ No encoding captured")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 MobileFaceNet Re-registration Tool")
    print("This will capture a fresh face encoding for Reshma")
    print("using the current MobileFaceNet system.")
    print()
    
    confirm = input("Re-register Reshma with MobileFaceNet? (y/n): ")
    if confirm.lower() == 'y':
        if reregister_reshma():
            print("\n🎉 Re-registration successful!")
            print("Reshma should now be recognized correctly.")
            print("Test the camera again to verify.")
        else:
            print("\n❌ Re-registration failed!")
    else:
        print("Cancelled.")
