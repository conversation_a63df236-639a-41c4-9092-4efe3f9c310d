#!/usr/bin/env python3
"""
Force load faces and test recognition with webcam
This will bypass the camera manager and directly test face recognition
"""

import sys
import os
import cv2
import numpy as np
import time

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def force_test_face_recognition():
    """Force test face recognition with direct webcam access"""
    print("🔧 Force Face Recognition Test")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Step 1: Load faces from database
        print("📂 Step 1: Loading faces from database...")
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.face_encoding.isnot(None)).all()
        
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
            print(f"   ✅ Found user: {user.name} (ID: {user.id})")
        
        if len(users_data) == 0:
            print("❌ No users with face encodings found!")
            return False
        
        # Step 2: Force load faces into recognition system
        print(f"\n🔄 Step 2: Force loading {len(users_data)} faces into recognition system...")
        face_recognition_system.load_known_faces(users_data)
        
        print(f"📊 Recognition system status:")
        print(f"   Known encodings: {len(face_recognition_system.known_encodings)}")
        print(f"   Known names: {face_recognition_system.known_names}")
        print(f"   Similarity threshold: {face_recognition_system.similarity_threshold}")
        
        if len(face_recognition_system.known_encodings) == 0:
            print("❌ Failed to load faces into recognition system!")
            return False
        
        # Step 3: Test webcam
        print(f"\n📹 Step 3: Testing webcam...")
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam!")
            return False
        
        print("✅ Webcam opened successfully")
        print("\n🎯 LIVE FACE RECOGNITION TEST")
        print("=" * 40)
        print("Show your face to the camera...")
        print("Press 'q' to quit, 's' to take a screenshot test")
        
        frame_count = 0
        last_recognition_time = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Cannot read frame")
                break
            
            frame_count += 1
            current_time = time.time()
            
            # Process every 10 frames (reduce CPU load)
            if frame_count % 10 == 0 and (current_time - last_recognition_time) > 1.0:
                last_recognition_time = current_time
                
                print(f"\n🔍 Frame {frame_count}: Testing face recognition...")
                
                # Test face recognition
                try:
                    if face_recognition_system.use_mediapipe:
                        results = face_recognition_system.mediapipe_system.recognize_faces_in_frame(frame)
                        
                        if len(results) > 0:
                            for i, result in enumerate(results):
                                name = result.get('name', 'Unknown')
                                confidence = result.get('confidence', 0.0)
                                is_known = result.get('is_known', False)
                                
                                print(f"   👤 Face {i+1}: {name}")
                                print(f"      Confidence: {confidence:.3f}")
                                print(f"      Known: {'✅' if is_known else '❌'}")
                                
                                if is_known:
                                    print(f"   🎉 SUCCESS! Recognized: {name}")
                                else:
                                    print(f"   ⚠️ Unknown person detected")
                        else:
                            print(f"   👻 No faces detected")
                    else:
                        print(f"   ❌ MediaPipe system not available")
                        
                except Exception as recognition_error:
                    print(f"   ❌ Recognition error: {recognition_error}")
            
            # Show frame
            cv2.imshow('Face Recognition Test', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save screenshot for debugging
                screenshot_path = f"debug_frame_{int(time.time())}.jpg"
                cv2.imwrite(screenshot_path, frame)
                print(f"📸 Screenshot saved: {screenshot_path}")
        
        cap.release()
        cv2.destroyAllWindows()
        db.close()
        
        print("\n✅ Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_similarity_test():
    """Quick test to check if similarity calculation works"""
    print("\n🧪 Quick Similarity Test")
    print("=" * 30)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Load faces
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.face_encoding.isnot(None)).all()
        
        if len(users) == 0:
            print("❌ No users found")
            return False
        
        user = users[0]
        encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
        
        print(f"👤 Testing with user: {user.name}")
        print(f"📊 Encoding shape: {encoding.shape}")
        print(f"📊 Encoding norm: {np.linalg.norm(encoding):.3f}")
        
        # Test self-similarity
        self_similarity = np.dot(encoding, encoding)
        print(f"🔍 Self-similarity: {self_similarity:.3f}")
        
        # Test with slight noise
        noisy_encoding = encoding + np.random.normal(0, 0.01, encoding.shape)
        noisy_encoding = noisy_encoding / np.linalg.norm(noisy_encoding)  # Normalize
        
        noisy_similarity = np.dot(encoding, noisy_encoding)
        print(f"🔍 Noisy similarity: {noisy_similarity:.3f}")
        
        threshold = face_recognition_system.similarity_threshold
        print(f"🎯 Threshold: {threshold}")
        print(f"✅ Self-similarity above threshold: {'Yes' if self_similarity >= threshold else 'No'}")
        print(f"✅ Noisy similarity above threshold: {'Yes' if noisy_similarity >= threshold else 'No'}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Face Recognition Force Test")
    print("This will directly test face recognition bypassing camera manager")
    print()
    
    # Quick similarity test first
    quick_similarity_test()
    
    print()
    confirm = input("Start live webcam test? (y/n): ")
    if confirm.lower() == 'y':
        force_test_face_recognition()
    else:
        print("Test cancelled.")
