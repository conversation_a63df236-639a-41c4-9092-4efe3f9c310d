#!/usr/bin/env python3
"""
Properly fix <PERSON><PERSON><PERSON>'s encoding by using the web interface registration system
"""

import sys
import os
import requests
import json
import base64
import cv2
import numpy as np

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def capture_and_register_reshma():
    """Capture Reshma's face and register using the web API"""
    print("🔧 Proper Reshma Registration Fix")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma:
            print("❌ Reshma not found in database!")
            return False
        
        print(f"✅ Found Reshma (ID: {reshma.id})")
        
        print("\n🎥 Starting webcam for face capture...")
        print("Position <PERSON><PERSON><PERSON>'s face clearly in front of the camera")
        print("Press SPACE to capture, 'q' to quit")
        
        # Open webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam!")
            return False
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        captured_image = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Show frame with instructions
            display_frame = frame.copy()
            cv2.putText(display_frame, "Position Reshma's face clearly", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press SPACE to capture", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press Q to quit", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imshow('Fix Reshma Registration', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Space to capture
                print("📸 Capturing image...")
                captured_image = frame.copy()
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        if captured_image is not None:
            print("✅ Image captured successfully!")
            
            # Convert image to base64 for API
            _, buffer = cv2.imencode('.jpg', captured_image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            
            # Call the registration API
            try:
                url = f"http://localhost:8000/api/users/{reshma.id}/register-face"
                
                payload = {
                    "image_data": f"data:image/jpeg;base64,{image_base64}"
                }
                
                print("🔄 Registering face via API...")
                response = requests.post(url, json=payload)
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ Face registration successful!")
                    print(f"   Response: {result}")
                    return True
                else:
                    print(f"❌ API call failed: {response.status_code}")
                    print(f"   Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ API call error: {e}")
                return False
        else:
            print("❌ No image captured")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_recognition_after_fix():
    """Test recognition after fixing the encoding"""
    print("\n🧪 Testing Recognition After Fix")
    print("=" * 40)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get updated Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma or not reshma.face_encoding:
            print("❌ Reshma not found or no encoding!")
            return False
        
        # Load the new encoding
        users_data = [{
            'id': reshma.id,
            'name': reshma.name,
            'face_encoding': reshma.face_encoding
        }]
        
        face_recognition_system.load_known_faces(users_data)
        
        print(f"✅ Loaded encoding for {reshma.name}")
        print(f"   System has {len(face_recognition_system.known_encodings)} encodings")
        print(f"   Known names: {face_recognition_system.known_names}")
        
        # Test self-similarity
        if len(face_recognition_system.known_encodings) > 0:
            encoding = face_recognition_system.known_encodings[0]
            self_similarity = np.dot(encoding, encoding)
            print(f"   Self-similarity: {self_similarity:.3f}")
            
            if self_similarity > 0.8:
                print("✅ Encoding looks good!")
            else:
                print("⚠️ Encoding might still have issues")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Proper Reshma Registration Fix")
    print("This will capture Reshma's face and register it using")
    print("the same system that the web interface uses.")
    print()
    
    confirm = input("Fix Reshma's registration? (y/n): ")
    if confirm.lower() == 'y':
        if capture_and_register_reshma():
            print("\n🎉 Registration fix successful!")
            
            # Test the fix
            if test_recognition_after_fix():
                print("\n✅ Recognition test passed!")
                print("Reshma should now be recognized correctly.")
                print("Test the camera again to verify.")
            else:
                print("\n⚠️ Recognition test had issues.")
        else:
            print("\n❌ Registration fix failed!")
    else:
        print("Cancelled.")
