#!/usr/bin/env python3
"""
Test if the registered encoding is compatible with the live recognition system
"""

import sys
import os
import numpy as np

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_encoding_compatibility():
    """Test if <PERSON><PERSON><PERSON>'s encoding is compatible"""
    print("🧪 Testing Encoding Compatibility")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma or not reshma.face_encoding:
            print("❌ Reshma not found or no encoding!")
            return False
        
        print(f"✅ Found Reshma (ID: {reshma.id})")
        
        # Analyze the stored encoding
        stored_encoding = np.frombuffer(reshma.face_encoding, dtype=np.float32)
        print(f"📊 Stored encoding analysis:")
        print(f"   Dimensions: {len(stored_encoding)}D")
        print(f"   Norm: {np.linalg.norm(stored_encoding):.3f}")
        print(f"   Mean: {np.mean(stored_encoding):.3f}")
        print(f"   Std: {np.std(stored_encoding):.3f}")
        print(f"   Min: {np.min(stored_encoding):.3f}")
        print(f"   Max: {np.max(stored_encoding):.3f}")
        
        # Check if it's normalized
        norm = np.linalg.norm(stored_encoding)
        is_normalized = 0.95 <= norm <= 1.05
        print(f"   Normalized: {'✅' if is_normalized else '❌'} ({norm:.3f})")
        
        # Load faces into recognition system
        print(f"\n🔄 Loading faces into recognition system...")
        users_data = [{
            'id': reshma.id,
            'name': reshma.name,
            'face_encoding': reshma.face_encoding
        }]
        
        face_recognition_system.load_known_faces(users_data)
        
        print(f"📊 Recognition system status:")
        print(f"   Known encodings: {len(face_recognition_system.known_encodings)}")
        print(f"   Known names: {face_recognition_system.known_names}")
        print(f"   Similarity threshold: {face_recognition_system.similarity_threshold}")
        
        if len(face_recognition_system.known_encodings) > 0:
            loaded_encoding = face_recognition_system.known_encodings[0]
            print(f"\n📊 Loaded encoding analysis:")
            print(f"   Dimensions: {len(loaded_encoding)}D")
            print(f"   Norm: {np.linalg.norm(loaded_encoding):.3f}")
            print(f"   Mean: {np.mean(loaded_encoding):.3f}")
            print(f"   Std: {np.std(loaded_encoding):.3f}")
            
            # Test self-similarity
            self_similarity = np.dot(loaded_encoding, loaded_encoding)
            print(f"\n🧪 Self-similarity test:")
            print(f"   Self-similarity: {self_similarity:.3f}")
            print(f"   Above threshold: {'✅' if self_similarity >= face_recognition_system.similarity_threshold else '❌'}")
            
            if self_similarity >= 0.99:
                print("✅ Encoding looks good for recognition!")
            else:
                print("⚠️ Encoding may have issues")
        else:
            print("❌ No encodings loaded into recognition system!")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_live_camera_loading():
    """Test if camera loading triggers face loading"""
    print("\n🎥 Testing Camera Face Loading")
    print("=" * 40)
    
    try:
        from app.services.camera_manager import camera_manager
        from app.core.database import SessionLocal
        from app.models import models
        
        # Simulate camera loading process
        db = SessionLocal()
        
        print("📂 Simulating camera face loading...")
        users = db.query(models.User).all()
        users_data = [
            {
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            }
            for user in users if user.face_encoding
        ]
        
        print(f"📊 Found {len(users_data)} users with encodings:")
        for user_data in users_data:
            print(f"   - {user_data['name']} (ID: {user_data['id']})")
        
        # Test loading into face recognition system
        from app.services.face_utils import face_recognition_system
        face_recognition_system.load_known_faces(users_data)
        
        print(f"\n✅ Face loading simulation complete")
        print(f"   Loaded: {len(face_recognition_system.known_encodings)} encodings")
        print(f"   Names: {face_recognition_system.known_names}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error in camera loading test: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Face Recognition Compatibility Test")
    print("This will check if Reshma's encoding is compatible with the live system")
    print()
    
    # Test encoding compatibility
    test1_success = test_encoding_compatibility()
    
    # Test camera loading
    test2_success = test_live_camera_loading()
    
    print("\n" + "=" * 60)
    if test1_success and test2_success:
        print("✅ All tests passed!")
        print("The encoding should be compatible.")
        print("\nIf recognition still fails, the issue might be:")
        print("1. Camera not loading faces properly")
        print("2. Different preprocessing between registration and live recognition")
        print("3. Threshold too high")
    else:
        print("❌ Some tests failed!")
        print("Check the errors above for details.")
    
    print("\nNext steps:")
    print("1. Restart the camera stream")
    print("2. Check terminal logs for similarity scores")
    print("3. If still 0.000, try re-registering with a clearer image")
