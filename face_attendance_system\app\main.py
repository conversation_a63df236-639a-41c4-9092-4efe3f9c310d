# Face Recognition Attendance System (CPU-only)

from fastapi import FastAPI, Request, UploadFile, Form, Depends, File, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse, JSONResponse, Response
from sqlalchemy.orm import Session
import shutil
import os
import io
from datetime import datetime, date
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from io import BytesIO
import base64
from .core.database import SessionLocal, engine
from .models import models
from .services import face_utils, attendance
from .services.camera_manager import camera_manager, test_available_cameras
from .services.attendance import attendance_service

models.Base.metadata.create_all(bind=engine)

app = FastAPI()
app.mount("/static", StaticFiles(directory="app/static"), name="static")
templates = Jinja2Templates(directory="app/templates")

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/", response_class=HTMLResponse)
async def root(request: Request, db: Session = Depends(get_db)):
    # Get currently logged in users
    current_users = attendance_service.get_current_logged_in_users(db)

    # Get today's attendance logs
    today = date.today().strftime("%Y-%m-%d")
    today_logs = attendance_service.get_attendance_logs(db, date_filter=today)

    # Get active cameras
    active_cameras = camera_manager.get_active_cameras()
    print(f"📹 Active cameras: {len(active_cameras)}")
    for cam in active_cameras:
        print(f"   - Camera ID: {cam.get('camera_id')}, Name: {cam.get('camera_name')}")

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "current_users": current_users,
        "today_logs": today_logs[:10],  # Show last 10 logs
        "active_cameras": active_cameras
    })

@app.get("/register-user", response_class=HTMLResponse)
async def get_register(request: Request):
    return templates.TemplateResponse("register_user.html", {"request": request})

@app.post("/register-user")
async def register_user(
    request: Request,
    name: str = Form(...),
    email: str = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    upload_folder = "app/static/uploads"
    os.makedirs(upload_folder, exist_ok=True)
    file_path = os.path.join(upload_folder, file.filename)

    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    encoding = face_utils.extract_face_encoding(file_path)
    if encoding is None:
        return templates.TemplateResponse("register_user.html", {
            "request": request,
            "message": "Face not found in image. Try again."
        })

    # Store web-accessible path
    web_image_path = f"/static/uploads/{file.filename}"

    user = models.User(
        name=name,
        email=email,
        image_path=web_image_path,
        face_encoding=encoding
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return RedirectResponse(url="/", status_code=302)

@app.get("/manage-camera", response_class=HTMLResponse)
async def camera_management(request: Request, db: Session = Depends(get_db)):
    cameras = db.query(models.Camera).all()
    return templates.TemplateResponse("manage_camera.html", {
        "request": request,
        "cameras": cameras
    })

@app.post("/manage-camera")
async def add_camera(
    request: Request,
    name: str = Form(...),
    camera_type: str = Form(...),
    url: str = Form(...),
    db: Session = Depends(get_db)
):
    camera = models.Camera(
        name=name,
        type=camera_type,
        url=url
    )
    db.add(camera)
    db.commit()
    db.refresh(camera)
    return RedirectResponse(url="/manage-camera", status_code=302)

@app.post("/start-camera/{camera_id}")
async def start_camera(camera_id: int, db: Session = Depends(get_db)):
    camera = db.query(models.Camera).filter(models.Camera.id == camera_id).first()
    if not camera:
        return JSONResponse({"error": "Camera not found"}, status_code=404)

    success = camera_manager.start_camera(
        camera.id, camera.name, camera.url, camera.type, db
    )

    if success:
        return JSONResponse({"message": f"Camera {camera.name} started successfully"})
    else:
        return JSONResponse({"error": f"Failed to start camera {camera.name}"}, status_code=500)

@app.post("/stop-camera/{camera_id}")
async def stop_camera(camera_id: int):
    success = camera_manager.stop_camera(camera_id)
    if success:
        return JSONResponse({"message": "Camera stopped successfully"})
    else:
        return JSONResponse({"error": "Camera not found or already stopped"}, status_code=404)

@app.get("/camera-stream/{camera_id}")
async def camera_stream(camera_id: int):
    import time
    print(f"🎥 Camera stream requested for camera ID: {camera_id}")

    def generate():
        frame_count = 0
        max_retries = 5
        retry_count = 0
        print(f"🔄 Starting frame generation for camera {camera_id}")

        while True:
            try:
                frame = camera_manager.get_camera_frame(camera_id)
                if frame:
                    retry_count = 0  # Reset retry count on successful frame
                    if frame_count % 30 == 0:  # Log every 30 frames
                        print(f"✅ Frame {frame_count} generated for camera {camera_id}")
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n'
                           b'Cache-Control: no-cache, no-store, must-revalidate\r\n'
                           b'Pragma: no-cache\r\n'
                           b'Expires: 0\r\n\r\n' + frame + b'\r\n')

                    # Control frame rate - limit to ~15 FPS for dashboard
                    time.sleep(0.067)  # ~15 FPS
                    frame_count += 1
                else:
                    retry_count += 1
                    print(f"❌ No frame from camera {camera_id}, retry {retry_count}/{max_retries}")
                    if retry_count >= max_retries:
                        print(f"🛑 Max retries reached for camera {camera_id}")
                        break
                    time.sleep(0.1)  # Wait before retry

            except Exception as e:
                print(f"Error in camera stream {camera_id}: {e}")
                retry_count += 1
                if retry_count >= max_retries:
                    break
                time.sleep(0.1)

    return StreamingResponse(
        generate(),
        media_type="multipart/x-mixed-replace; boundary=frame",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/attendance", response_class=HTMLResponse)
async def attendance_logs(request: Request, db: Session = Depends(get_db)):
    logs = attendance_service.get_attendance_logs(db)
    # Get all registered users for the filter dropdown
    users = db.query(models.User).filter(models.User.is_active == True).all()
    return templates.TemplateResponse("attendance.html", {
        "request": request,
        "logs": logs,
        "users": users
    })

@app.get("/attendance/filter")
async def filter_attendance(
    date_filter: str = None,
    user_id: int = None,
    camera_type: str = None,
    db: Session = Depends(get_db)
):
    logs = attendance_service.get_attendance_logs(db, date_filter, user_id)
    # Apply camera type filter if provided
    if camera_type:
        logs = [log for log in logs if log.get('camera_type') == camera_type]
    return JSONResponse({"logs": logs})

@app.get("/attendance/export-pdf")
async def export_attendance_pdf(
    date_filter: str = None,
    user_id: int = None,
    camera_type: str = None,
    db: Session = Depends(get_db)
):
    """Export attendance logs as PDF with optional filters"""
    # Get filtered attendance logs
    logs = attendance_service.get_attendance_logs(db, date_filter, user_id)

    # Apply camera type filter if provided
    if camera_type:
        logs = [log for log in logs if log.get('camera_type') == camera_type]

    # Get user info if filtering by specific user
    user_info = None
    if user_id:
        user_info = db.query(models.User).filter(models.User.id == user_id).first()

    # Create PDF
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )

    # Determine title based on filters
    title_text = "Attendance Report"
    if user_info:
        title_text = f"Attendance Report - {user_info.name}"
    elif date_filter:
        title_text = f"Attendance Report - {date_filter}"

    story.append(Paragraph(title_text, title_style))
    story.append(Spacer(1, 20))

    # Filter information
    filter_info_style = ParagraphStyle(
        'FilterInfo',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=15
    )

    filter_text = "Filters Applied: "
    filters_applied = []
    if date_filter:
        filters_applied.append(f"Date: {date_filter}")
    if user_info:
        filters_applied.append(f"User: {user_info.name}")
    if camera_type:
        filters_applied.append(f"Camera Type: {camera_type}")

    if filters_applied:
        filter_text += ", ".join(filters_applied)
    else:
        filter_text += "None (All Records)"

    story.append(Paragraph(filter_text, filter_info_style))
    story.append(Spacer(1, 10))

    # Summary statistics
    summary_style = ParagraphStyle(
        'Summary',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=15
    )

    total_records = len(logs)
    unique_users = len(set(log['name'] for log in logs))
    unique_dates = len(set(log['date'] for log in logs))

    story.append(Paragraph("<b>Summary Statistics</b>", styles['Heading2']))
    story.append(Paragraph(f"<b>Total Records:</b> {total_records}", summary_style))
    story.append(Paragraph(f"<b>Unique Users:</b> {unique_users}", summary_style))
    story.append(Paragraph(f"<b>Date Range:</b> {unique_dates} unique dates", summary_style))
    story.append(Spacer(1, 20))

    # Attendance Logs Table
    if logs:
        story.append(Paragraph("<b>Attendance Records</b>", styles['Heading2']))

        # Table headers
        table_data = [['Name', 'Email', 'Date', 'Login', 'Logout', 'Duration', 'Camera', 'Type']]

        # Table rows
        for log in logs:
            table_data.append([
                log['name'],
                log['email'],
                log['date'],
                log['login_time'] or '-',
                log['logout_time'] or '-',
                log['duration'] or 'Active',
                log['camera_name'],
                log['camera_type']
            ])

        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 7),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(table)
    else:
        story.append(Paragraph("No attendance records found for the selected filters.", summary_style))

    # Build PDF
    doc.build(story)
    buffer.seek(0)

    # Generate filename
    filename = "attendance_report"
    if date_filter:
        filename += f"_{date_filter}"
    if user_info:
        filename += f"_{user_info.name.replace(' ', '_')}"
    filename += ".pdf"

    # Return PDF as response
    return Response(
        content=buffer.getvalue(),
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@app.get("/users", response_class=HTMLResponse)
async def users_list(request: Request, db: Session = Depends(get_db)):
    users = db.query(models.User).all()
    return templates.TemplateResponse("users.html", {
        "request": request,
        "users": users
    })

@app.get("/user/{user_id}", response_class=HTMLResponse)
async def user_details(request: Request, user_id: int, db: Session = Depends(get_db)):
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return RedirectResponse(url="/users", status_code=302)

    # Get user's attendance logs
    user_logs = attendance_service.get_attendance_logs(db, user_id=user_id)

    # Calculate statistics
    total_days = len(set(log['date'] for log in user_logs))
    total_hours = sum(
        float(log['duration'].split(':')[0]) + float(log['duration'].split(':')[1])/60
        for log in user_logs if log['duration']
    )

    return templates.TemplateResponse("user_details.html", {
        "request": request,
        "user": user,
        "logs": user_logs,
        "stats": {
            "total_days": total_days,
            "total_hours": round(total_hours, 2)
        }
    })

@app.get("/user/{user_id}/export-pdf")
async def export_user_pdf(user_id: int, db: Session = Depends(get_db)):
    """Export user details and attendance logs as PDF"""
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return JSONResponse({"error": "User not found"}, status_code=404)

    # Get user's attendance logs
    user_logs = attendance_service.get_attendance_logs(db, user_id=user_id)

    # Calculate statistics
    total_days = len(set(log['date'] for log in user_logs))
    total_hours = sum(
        float(log['duration'].split(':')[0]) + float(log['duration'].split(':')[1])/60
        for log in user_logs if log['duration']
    )

    # Create PDF
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    story.append(Paragraph(f"User Report: {user.name}", title_style))
    story.append(Spacer(1, 20))

    # User Information Section
    user_info_style = ParagraphStyle(
        'UserInfo',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=10
    )

    story.append(Paragraph("<b>User Information</b>", styles['Heading2']))
    story.append(Paragraph(f"<b>Name:</b> {user.name}", user_info_style))
    story.append(Paragraph(f"<b>Email:</b> {user.email}", user_info_style))
    story.append(Paragraph(f"<b>Status:</b> {'Active' if user.is_active else 'Inactive'}", user_info_style))
    story.append(Paragraph(f"<b>Registered:</b> {user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown'}", user_info_style))
    story.append(Spacer(1, 20))

    # Statistics Section
    story.append(Paragraph("<b>Attendance Statistics</b>", styles['Heading2']))
    story.append(Paragraph(f"<b>Total Days:</b> {total_days}", user_info_style))
    story.append(Paragraph(f"<b>Total Hours:</b> {round(total_hours, 2)}", user_info_style))
    story.append(Paragraph(f"<b>Average Hours/Day:</b> {round(total_hours / total_days, 1) if total_days > 0 else 0}", user_info_style))
    story.append(Paragraph(f"<b>Total Records:</b> {len(user_logs)}", user_info_style))
    story.append(Spacer(1, 20))

    # Attendance Logs Table
    if user_logs:
        story.append(Paragraph("<b>Attendance History</b>", styles['Heading2']))

        # Table headers
        table_data = [['Date', 'Login Time', 'Logout Time', 'Duration', 'Camera', 'Type']]

        # Table rows
        for log in user_logs:
            table_data.append([
                log['date'],
                log['login_time'] or '-',
                log['logout_time'] or '-',
                log['duration'] or 'Active',
                log['camera_name'],
                log['camera_type']
            ])

        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(table)
    else:
        story.append(Paragraph("No attendance records found for this user.", user_info_style))

    # Build PDF
    doc.build(story)
    buffer.seek(0)

    # Return PDF as response
    return Response(
        content=buffer.getvalue(),
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={user.name}_attendance_report.pdf"}
    )

@app.post("/user/{user_id}/edit")
async def edit_user(
    user_id: int,
    name: str = Form(...),
    email: str = Form(...),
    file: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return JSONResponse({"error": "User not found"}, status_code=404)

    user.name = name
    user.email = email

    # Update image if provided
    if file and file.filename:
        upload_folder = "app/static/uploads"
        os.makedirs(upload_folder, exist_ok=True)
        file_path = os.path.join(upload_folder, f"user_{user_id}_{file.filename}")

        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Extract new face encoding
        encoding = face_utils.extract_face_encoding(file_path)
        if encoding:
            # Store web-accessible path
            user.image_path = f"/static/uploads/user_{user_id}_{file.filename}"
            user.face_encoding = encoding
        else:
            return JSONResponse({"error": "No face found in image"}, status_code=400)

    db.commit()
    return RedirectResponse(url=f"/user/{user_id}", status_code=302)

@app.delete("/user/{user_id}/delete")
async def delete_user(user_id: int, db: Session = Depends(get_db)):
    """Delete a user and all their attendance records"""
    try:
        # Find the user
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user:
            return JSONResponse({
                "status": "error",
                "message": "User not found"
            }, status_code=404)

        # Store user name for response
        user_name = user.name

        # Delete user's image file if it exists
        if user.image_path:
            # Convert web path to file system path
            image_filename = os.path.basename(user.image_path)
            file_path = os.path.join("app/static/uploads", image_filename)
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"Warning: Could not delete image file {file_path}: {e}")

        # Delete all attendance logs for this user
        db.query(models.Attendance).filter(models.Attendance.user_id == user_id).delete()

        # Delete the user
        db.delete(user)
        db.commit()

        return JSONResponse({
            "status": "success",
            "message": f"User '{user_name}' and all associated records have been deleted successfully"
        })

    except Exception as e:
        db.rollback()
        return JSONResponse({
            "status": "error",
            "message": f"An error occurred while deleting the user: {str(e)}"
        }, status_code=500)

@app.get("/fix-user-status")
async def fix_user_status(db: Session = Depends(get_db)):
    """Fix user status - set all users to active"""
    try:
        # Update all users to be active
        users = db.query(models.User).all()
        for user in users:
            if user.is_active is None:
                user.is_active = True

        db.commit()

        return JSONResponse({
            "status": "success",
            "message": f"Updated {len(users)} users to active status"
        })
    except Exception as e:
        db.rollback()
        return JSONResponse({
            "status": "error",
            "message": f"Error updating user status: {str(e)}"
        }, status_code=500)

@app.get("/test-cameras")
async def test_cameras():
    """Test available camera indices"""
    try:
        available_cameras = test_available_cameras()
        return JSONResponse({
            "status": "success",
            "available_cameras": available_cameras,
            "message": f"Found {len(available_cameras)} available cameras"
        })
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/debug-cameras")
async def debug_cameras(db: Session = Depends(get_db)):
    """Debug camera status"""
    try:
        # Get cameras from database
        db_cameras = db.query(models.Camera).all()

        # Get active cameras from manager
        active_cameras = camera_manager.get_active_cameras()

        return JSONResponse({
            "database_cameras": [
                {
                    "id": cam.id,
                    "name": cam.name,
                    "camera_type": cam.camera_type,
                    "url": cam.url,
                    "is_active": cam.is_active
                } for cam in db_cameras
            ],
            "active_cameras_manager": active_cameras,
            "camera_manager_active_count": len(camera_manager.active_cameras),
            "camera_manager_active_ids": list(camera_manager.active_cameras.keys())
        })
    except Exception as e:
        return JSONResponse({
            "error": str(e)
        }, status_code=500)

@app.post("/toggle-face-recognition/{camera_id}")
async def toggle_face_recognition(camera_id: int, enable: bool = True):
    """Toggle face recognition for a specific camera"""
    try:
        if camera_id in camera_manager.active_cameras:
            camera_stream = camera_manager.active_cameras[camera_id]
            camera_stream.enable_face_recognition = enable
            status = "enabled" if enable else "disabled"
            return JSONResponse({
                "message": f"Face recognition {status} for camera {camera_id}",
                "camera_id": camera_id,
                "face_recognition_enabled": enable
            })
        else:
            return JSONResponse({
                "error": f"Camera {camera_id} not found or not active"
            }, status_code=404)
    except Exception as e:
        return JSONResponse({
            "error": str(e)
        }, status_code=500)

@app.post("/cleanup-cameras")
async def cleanup_cameras():
    """Clean up zombie cameras and return status"""
    try:
        zombie_count = camera_manager.cleanup_zombie_cameras()
        active_cameras = camera_manager.get_active_cameras()
        return JSONResponse({
            "message": f"Cleaned up {zombie_count} zombie cameras",
            "zombie_cameras_removed": zombie_count,
            "active_cameras": active_cameras
        })
    except Exception as e:
        return JSONResponse({
            "error": str(e)
        }, status_code=500)

@app.post("/reload-faces")
async def reload_faces(db: Session = Depends(get_db)):
    """Manually reload faces into recognition system"""
    try:
        # Load faces from database
        users = db.query(models.User).filter(models.User.face_encoding.isnot(None)).all()
        users_data = [
            {
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            }
            for user in users
        ]

        # Load into recognition system
        face_utils.face_recognition_system.load_known_faces(users_data)

        return JSONResponse({
            "message": f"Successfully reloaded {len(users_data)} faces",
            "users": [user['name'] for user in users_data],
            "known_names": face_utils.face_recognition_system.known_names if hasattr(face_utils.face_recognition_system, 'known_names') else [],
            "known_encodings_count": len(face_utils.face_recognition_system.known_encodings) if hasattr(face_utils.face_recognition_system, 'known_encodings') else 0
        })
    except Exception as e:
        return JSONResponse({
            "error": str(e)
        }, status_code=500)

@app.get("/debug-camera-startup/{camera_id}")
async def debug_camera_startup(camera_id: int, db: Session = Depends(get_db)):
    """Debug camera startup process"""
    try:
        # Get camera from database
        camera = db.query(models.Camera).filter(models.Camera.id == camera_id).first()
        if not camera:
            return JSONResponse({
                "error": "Camera not found"
            }, status_code=404)

        # Test camera source directly
        import cv2
        try:
            if camera.url.isdigit():
                camera_source = int(camera.url)
            else:
                camera_source = camera.url

            cap = cv2.VideoCapture(camera_source)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    result = {
                        "camera_source_test": "PASSED",
                        "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
                        "frame_shape": str(frame.shape),
                        "camera_id": camera_id,
                        "camera_name": camera.name,
                        "camera_url": camera.url,
                        "parsed_source": str(camera_source)
                    }
                    cap.release()
                    return JSONResponse(result)
                else:
                    cap.release()
                    return JSONResponse({
                        "camera_source_test": "FAILED",
                        "error": "Cannot read frames",
                        "camera_id": camera_id,
                        "camera_name": camera.name,
                        "camera_url": camera.url,
                        "parsed_source": str(camera_source)
                    })
            else:
                cap.release()
                return JSONResponse({
                    "camera_source_test": "FAILED",
                    "error": "Cannot open camera",
                    "camera_id": camera_id,
                    "camera_name": camera.name,
                    "camera_url": camera.url,
                    "parsed_source": str(camera_source)
                })
        except Exception as test_error:
            return JSONResponse({
                "camera_source_test": "EXCEPTION",
                "error": str(test_error),
                "camera_id": camera_id,
                "camera_name": camera.name,
                "camera_url": camera.url
            })

    except Exception as e:
        return JSONResponse({
            "error": str(e)
        }, status_code=500)

@app.get("/simple-camera-test/{camera_index}")
async def simple_camera_test(camera_index: int):
    """Simple camera test without any face recognition"""
    try:
        import cv2

        print(f"🔍 Testing camera index {camera_index}")

        # Test camera
        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            print(f"❌ Cannot open camera index {camera_index}")
            return JSONResponse({
                "status": "failed",
                "error": f"Cannot open camera index {camera_index}",
                "camera_index": camera_index
            })

        print(f"✅ Camera {camera_index} opened successfully")

        # Try to read a frame
        ret, frame = cap.read()

        if not ret or frame is None:
            print(f"❌ Camera {camera_index} opened but cannot read frames")
            cap.release()
            return JSONResponse({
                "status": "failed",
                "error": f"Camera {camera_index} opened but cannot read frames",
                "camera_index": camera_index
            })

        print(f"✅ Camera {camera_index} can read frames - Shape: {frame.shape}")

        # Get camera properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        cap.release()

        return JSONResponse({
            "status": "success",
            "camera_index": camera_index,
            "resolution": f"{width}x{height}",
            "fps": fps,
            "frame_shape": f"{frame.shape}",
            "message": f"Camera {camera_index} is working!"
        })

    except Exception as e:
        print(f"❌ Exception testing camera {camera_index}: {e}")
        return JSONResponse({
            "status": "error",
            "error": str(e),
            "camera_index": camera_index
        })

@app.get("/minimal-camera-stream/{camera_index}")
async def minimal_camera_stream(camera_index: int):
    """Minimal camera stream without any processing"""
    import cv2

    def generate_frames():
        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            print(f"❌ Cannot open camera {camera_index} for streaming")
            return

        print(f"✅ Starting minimal stream for camera {camera_index}")

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print(f"❌ Failed to read frame from camera {camera_index}")
                    break

                # Encode frame as JPEG
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                frame_bytes = buffer.tobytes()

                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

        except Exception as e:
            print(f"❌ Error in minimal stream: {e}")
        finally:
            cap.release()
            print(f"🔄 Released camera {camera_index}")

    return StreamingResponse(generate_frames(), media_type="multipart/x-mixed-replace; boundary=frame")

@app.get("/camera-test", response_class=HTMLResponse)
async def camera_test_page(request: Request):
    """Camera test page"""
    return templates.TemplateResponse("camera_test.html", {"request": request})

@app.delete("/delete-camera/{camera_id}")
async def delete_camera(camera_id: int, request: Request, db: Session = Depends(get_db)):
    """Delete a camera"""
    try:
        # Check if force delete is requested
        force_delete = request.query_params.get('force', '').lower() == 'true'

        # Find the camera
        camera = db.query(models.Camera).filter(models.Camera.id == camera_id).first()
        if not camera:
            return JSONResponse({
                "status": "error",
                "message": "Camera not found"
            }, status_code=404)

        camera_name = camera.name

        # Stop the camera if it's currently running
        if camera_id in camera_manager.active_cameras:
            try:
                camera_manager.stop_camera(camera_id)
            except Exception as e:
                # Log the error but continue with deletion
                print(f"Warning: Failed to stop camera {camera_id} before deletion: {e}")

        # Check if there are any attendance records associated with this camera
        attendance_count = db.query(models.Attendance).filter(models.Attendance.camera_id == camera_id).count()

        if attendance_count > 0 and not force_delete:
            # Don't delete the camera if it has attendance records unless force delete
            return JSONResponse({
                "status": "error",
                "message": f"Cannot delete camera '{camera_name}' because it has {attendance_count} attendance records. Please delete attendance records first or contact administrator."
            }, status_code=400)

        # If force delete, delete attendance records first
        if force_delete and attendance_count > 0:
            db.query(models.Attendance).filter(models.Attendance.camera_id == camera_id).delete()
            print(f"Force deleted {attendance_count} attendance records for camera {camera_id}")

        # Delete the camera
        db.delete(camera)
        db.commit()

        message = f"Camera '{camera_name}' deleted successfully"
        if force_delete and attendance_count > 0:
            message += f" (along with {attendance_count} attendance records)"

        return JSONResponse({
            "status": "success",
            "message": message
        })

    except Exception as e:
        db.rollback()
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/get-camera/{camera_id}")
async def get_camera(camera_id: int, db: Session = Depends(get_db)):
    """Get camera details for editing"""
    try:
        camera = db.query(models.Camera).filter(models.Camera.id == camera_id).first()
        if not camera:
            return JSONResponse({
                "status": "error",
                "message": "Camera not found"
            }, status_code=404)

        return JSONResponse({
            "status": "success",
            "camera": {
                "id": camera.id,
                "name": camera.name,
                "url": camera.url,
                "type": camera.type
            }
        })
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.put("/edit-camera/{camera_id}")
async def edit_camera(camera_id: int, name: str = Form(...), url: str = Form(...),
                     camera_type: str = Form(...), db: Session = Depends(get_db)):
    """Edit camera details"""
    try:
        # Find the camera
        camera = db.query(models.Camera).filter(models.Camera.id == camera_id).first()
        if not camera:
            return JSONResponse({
                "status": "error",
                "message": "Camera not found"
            }, status_code=404)

        old_name = camera.name

        # Stop camera if it's running (will need to restart with new settings)
        was_active = camera_id in camera_manager.active_cameras
        if was_active:
            camera_manager.stop_camera(camera_id)

        # Update camera details
        camera.name = name.strip()
        camera.url = url.strip()
        camera.type = camera_type

        db.commit()

        # If camera was active, restart it with new settings
        if was_active:
            try:
                camera_manager.start_camera(camera_id, name, url, camera_type, db)
            except Exception as restart_error:
                print(f"Warning: Failed to restart camera after edit: {restart_error}")

        return JSONResponse({
            "status": "success",
            "message": f"Camera '{old_name}' updated successfully"
        })

    except Exception as e:
        db.rollback()
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/api/attendance/recent")
async def get_recent_attendance(db: Session = Depends(get_db)):
    """Get recent attendance logs (last 10 entries)"""
    try:
        recent_logs = attendance_service.get_attendance_logs(db)[:10]
        return JSONResponse({
            "status": "success",
            "logs": recent_logs,
            "count": len(recent_logs)
        })
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/api/attendance/current")
async def get_current_logged_in(db: Session = Depends(get_db)):
    """Get currently logged in users"""
    try:
        current_users = attendance_service.get_current_logged_in_users(db)
        return JSONResponse({
            "status": "success",
            "users": current_users,
            "count": len(current_users)
        })
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/api/users/registered")
async def get_registered_users(db: Session = Depends(get_db)):
    """Get list of registered users with face encodings"""
    try:
        users = db.query(models.User).filter(models.User.face_encoding.isnot(None)).all()
        user_list = [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "has_face_encoding": user.face_encoding is not None
            }
            for user in users
        ]
        return JSONResponse({
            "status": "success",
            "users": user_list,
            "count": len(user_list)
        })
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)
