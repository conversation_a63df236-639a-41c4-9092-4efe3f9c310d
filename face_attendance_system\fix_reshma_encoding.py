#!/usr/bin/env python3
"""
Fix Reshma's encoding by re-registering with current MobileFaceNet system
"""

import sys
import os
import cv2
import numpy as np

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def fix_reshma_encoding():
    """Re-register Reshma with current MobileFaceNet system"""
    print("🔧 Fix Reshma's Face Encoding")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get Reshma from database
        db = SessionLocal()
        reshma = db.query(models.User).filter(models.User.name == 'Reshma').first()
        
        if not reshma:
            print("❌ Reshma not found in database!")
            return False
        
        print(f"✅ Found Reshma (ID: {reshma.id})")
        
        # Check current encoding
        if reshma.face_encoding:
            current_encoding = np.frombuffer(reshma.face_encoding, dtype=np.float32)
            print(f"📊 Current encoding: {len(current_encoding)}D, norm: {np.linalg.norm(current_encoding):.3f}")
        else:
            print("📊 No current encoding")
        
        print("\n🎥 Starting webcam for face capture...")
        print("Position Reshma's face in front of the camera")
        print("Press SPACE to capture, 'q' to quit")
        
        # Open webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam!")
            return False
        
        captured_encoding = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Show frame with instructions
            display_frame = frame.copy()
            cv2.putText(display_frame, "Position face clearly", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press SPACE to capture", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press Q to quit", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.imshow('Fix Reshma Encoding', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Space to capture
                print("📸 Capturing face...")
                
                try:
                    # Use current MobileFaceNet system to extract embedding
                    if face_recognition_system.use_mediapipe:
                        # Extract embedding using MediaPipe + MobileFaceNet
                        results = face_recognition_system.mediapipe_system.recognize_faces_in_frame(frame)
                        
                        if len(results) == 0:
                            print("❌ No face detected! Try again.")
                            continue
                        elif len(results) > 1:
                            print("⚠️ Multiple faces detected! Please ensure only Reshma's face is visible.")
                            continue
                        
                        # Extract the face region
                        result = results[0]
                        top, right, bottom, left = result['location']
                        face_crop = frame[top:bottom, left:right]
                        
                        if face_crop.size == 0:
                            print("❌ Invalid face crop! Try again.")
                            continue
                        
                        # Extract fresh MobileFaceNet embedding directly
                        embedding = face_recognition_system.mediapipe_system.extract_face_embedding_openvino(face_crop)
                        
                        if embedding is not None and len(embedding) == 128:
                            captured_encoding = embedding
                            print(f"✅ Captured fresh MobileFaceNet encoding!")
                            print(f"   Dimensions: {len(embedding)}D")
                            print(f"   Norm: {np.linalg.norm(embedding):.3f}")
                            print(f"   Mean: {np.mean(embedding):.3f}")
                            print(f"   Std: {np.std(embedding):.3f}")
                            
                            # Test self-similarity
                            self_sim = np.dot(embedding, embedding)
                            print(f"   Self-similarity: {self_sim:.3f}")
                            
                            break
                        else:
                            print("❌ Failed to extract MobileFaceNet embedding! Try again.")
                    else:
                        print("❌ MobileFaceNet system not available!")
                        break
                        
                except Exception as e:
                    print(f"❌ Error extracting embedding: {e}")
                    continue
        
        cap.release()
        cv2.destroyAllWindows()
        
        if captured_encoding is not None:
            # Update Reshma's encoding in database
            try:
                # Convert to bytes
                encoding_bytes = captured_encoding.astype(np.float32).tobytes()
                
                # Update database
                reshma.face_encoding = encoding_bytes
                db.commit()
                
                print(f"\n✅ Successfully updated Reshma's face encoding!")
                print(f"   New encoding: {len(captured_encoding)}D MobileFaceNet")
                
                # Test the new encoding immediately
                print(f"\n🧪 Testing new encoding...")
                
                # Load the new encoding into recognition system
                users_data = [{
                    'id': reshma.id,
                    'name': reshma.name,
                    'face_encoding': encoding_bytes
                }]
                
                face_recognition_system.load_known_faces(users_data)
                print(f"✅ Loaded new encoding into recognition system")
                print(f"   Known names: {face_recognition_system.known_names}")
                
                # Test similarity with the same encoding
                if len(face_recognition_system.known_encodings) > 0:
                    loaded_encoding = face_recognition_system.known_encodings[0]
                    test_similarity = np.dot(captured_encoding, loaded_encoding)
                    print(f"   Test similarity: {test_similarity:.3f}")
                    
                    if test_similarity > 0.5:
                        print(f"✅ New encoding should work perfectly!")
                    else:
                        print(f"⚠️ Similarity might still be low: {test_similarity:.3f}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error updating database: {e}")
                db.rollback()
                return False
        else:
            print("❌ No encoding captured")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Reshma Encoding Fix Tool")
    print("This will capture a fresh face encoding for Reshma")
    print("using the current MobileFaceNet system to fix the")
    print("negative similarity issue.")
    print()
    
    confirm = input("Fix Reshma's encoding? (y/n): ")
    if confirm.lower() == 'y':
        if fix_reshma_encoding():
            print("\n🎉 Encoding fix successful!")
            print("Reshma should now be recognized correctly.")
            print("Test the camera again to verify.")
        else:
            print("\n❌ Encoding fix failed!")
    else:
        print("Cancelled.")
