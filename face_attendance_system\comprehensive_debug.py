#!/usr/bin/env python3
"""
Comprehensive debug to find why face recognition still shows unknown
"""

import sys
import os
import numpy as np

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def comprehensive_debug():
    """Complete investigation of the face recognition pipeline"""
    print("🔍 COMPREHENSIVE FACE RECOGNITION DEBUG")
    print("=" * 60)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Step 1: Database Investigation
        print("📂 STEP 1: DATABASE INVESTIGATION")
        print("-" * 40)
        db = SessionLocal()
        users = db.query(models.User).all()
        users_with_encodings = [u for u in users if u.face_encoding]
        
        print(f"Total users in database: {len(users)}")
        print(f"Users with face encodings: {len(users_with_encodings)}")
        
        if len(users_with_encodings) == 0:
            print("❌ CRITICAL: No users have face encodings!")
            print("   This means registration is not working properly.")
            return False
        
        for user in users_with_encodings:
            encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
            print(f"  👤 {user.name} (ID: {user.id})")
            print(f"     Encoding: {len(encoding)}D")
            print(f"     Norm: {np.linalg.norm(encoding):.6f}")
            print(f"     Mean: {np.mean(encoding):.6f}")
            print(f"     Std: {np.std(encoding):.6f}")
            print(f"     Range: [{np.min(encoding):.3f}, {np.max(encoding):.3f}]")
        
        # Step 2: Face Recognition System State
        print(f"\n🧠 STEP 2: FACE RECOGNITION SYSTEM STATE")
        print("-" * 45)
        print(f"System type: {type(face_recognition_system)}")
        print(f"System ID: {id(face_recognition_system)}")
        print(f"Use MediaPipe: {getattr(face_recognition_system, 'use_mediapipe', 'N/A')}")
        
        # Check main system
        if hasattr(face_recognition_system, 'known_encodings'):
            print(f"Main system known encodings: {len(face_recognition_system.known_encodings)}")
        if hasattr(face_recognition_system, 'known_names'):
            print(f"Main system known names: {face_recognition_system.known_names}")
        if hasattr(face_recognition_system, 'similarity_threshold'):
            print(f"Main system threshold: {face_recognition_system.similarity_threshold}")
        
        # Check MediaPipe system
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            print(f"\nMediaPipe system type: {type(mp_system)}")
            print(f"MediaPipe system ID: {id(mp_system)}")
            
            if hasattr(mp_system, 'known_encodings'):
                print(f"MP known encodings: {len(mp_system.known_encodings)}")
            if hasattr(mp_system, 'known_names'):
                print(f"MP known names: {mp_system.known_names}")
            if hasattr(mp_system, 'similarity_threshold'):
                print(f"MP threshold: {mp_system.similarity_threshold}")
        
        # Step 3: Force Load and Verify
        print(f"\n🔄 STEP 3: FORCE LOAD AND VERIFY")
        print("-" * 35)
        
        users_data = []
        for user in users_with_encodings:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        print(f"Preparing to load {len(users_data)} users...")
        for user_data in users_data:
            print(f"  - {user_data['name']} (ID: {user_data['id']})")
        
        # Load faces
        face_recognition_system.load_known_faces(users_data)
        
        # Verify loading
        print(f"\nAfter loading:")
        if hasattr(face_recognition_system, 'known_encodings'):
            print(f"  Main system encodings: {len(face_recognition_system.known_encodings)}")
        if hasattr(face_recognition_system, 'known_names'):
            print(f"  Main system names: {face_recognition_system.known_names}")
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            if hasattr(mp_system, 'known_encodings'):
                print(f"  MP system encodings: {len(mp_system.known_encodings)}")
            if hasattr(mp_system, 'known_names'):
                print(f"  MP system names: {mp_system.known_names}")
        
        # Step 4: Test Similarity Calculation
        print(f"\n🧪 STEP 4: SIMILARITY CALCULATION TEST")
        print("-" * 40)
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            if hasattr(mp_system, 'known_encodings') and len(mp_system.known_encodings) > 0:
                test_encoding = mp_system.known_encodings[0]
                test_name = mp_system.known_names[0]
                
                print(f"Testing with {test_name}'s encoding:")
                print(f"  Shape: {test_encoding.shape}")
                print(f"  Norm: {np.linalg.norm(test_encoding):.6f}")
                print(f"  Type: {type(test_encoding)}")
                
                # Test self-similarity
                self_similarity = np.dot(test_encoding, test_encoding)
                print(f"  Self-similarity: {self_similarity:.6f}")
                
                # Test with normalized version
                normalized = test_encoding / np.linalg.norm(test_encoding)
                norm_self_sim = np.dot(normalized, normalized)
                print(f"  Normalized self-similarity: {norm_self_sim:.6f}")
                
                # Test threshold comparison
                threshold = mp_system.similarity_threshold
                print(f"  Threshold: {threshold}")
                print(f"  Self-similarity > threshold: {self_similarity > threshold}")
                print(f"  Normalized > threshold: {norm_self_sim > threshold}")
        
        # Step 5: Check Camera Manager Integration
        print(f"\n📹 STEP 5: CAMERA MANAGER INTEGRATION")
        print("-" * 40)
        
        # Check if camera manager has its own instance
        try:
            from app.services.camera_manager import CameraManager
            print(f"CameraManager class available: ✅")
            
            # Check if there are any active camera instances
            print(f"Checking for active camera instances...")
            
        except Exception as e:
            print(f"Error importing CameraManager: {e}")
        
        # Step 6: Test Recognition Function Directly
        print(f"\n🎯 STEP 6: DIRECT RECOGNITION TEST")
        print("-" * 35)
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            if hasattr(mp_system, 'known_encodings') and len(mp_system.known_encodings) > 0:
                # Create a test embedding (same as stored)
                test_embedding = mp_system.known_encodings[0].copy()
                
                print(f"Testing recognition with stored embedding...")
                
                # Test the recognize_face method directly
                if hasattr(mp_system, 'recognize_face'):
                    result = mp_system.recognize_face(test_embedding)
                    print(f"Recognition result: {result}")
                else:
                    print("No recognize_face method found")
                
                # Manual similarity calculation
                similarities = []
                for i, known_encoding in enumerate(mp_system.known_encodings):
                    similarity = np.dot(test_embedding, known_encoding)
                    similarities.append(similarity)
                    print(f"  Similarity with {mp_system.known_names[i]}: {similarity:.6f}")
                
                if similarities:
                    best_match_idx = np.argmax(similarities)
                    best_similarity = similarities[best_match_idx]
                    best_name = mp_system.known_names[best_match_idx]
                    
                    print(f"  Best match: {best_name} ({best_similarity:.6f})")
                    print(f"  Above threshold ({mp_system.similarity_threshold}): {best_similarity > mp_system.similarity_threshold}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_camera_stream_logs():
    """Check what happens during camera streaming"""
    print(f"\n📺 CAMERA STREAM LOG ANALYSIS")
    print("=" * 40)
    print("Please start the camera stream and watch for these logs:")
    print("1. 🔄 FORCE RELOADING FACES for camera...")
    print("2. ✅ VERIFICATION: Recognition system now has X encodings")
    print("3. 🔍 Similarity with [name]: X.XXX (threshold: X.X)")
    print("4. 🔍 Face result: name='...', is_known=..., confidence=...")
    print()
    print("If you don't see the FORCE RELOADING logs, the camera")
    print("manager is not loading faces properly.")
    print()
    print("If similarity is still negative, there's an encoding")
    print("compatibility issue.")

if __name__ == "__main__":
    print("🚀 Comprehensive Face Recognition Debug")
    print("This will investigate every aspect of the face recognition system")
    print()
    
    if comprehensive_debug():
        check_camera_stream_logs()
        print("\n" + "=" * 60)
        print("🎯 DEBUG COMPLETE")
        print()
        print("Next steps:")
        print("1. Check the debug output above for any issues")
        print("2. Start camera stream and watch terminal logs")
        print("3. Look for the specific log patterns mentioned")
    else:
        print("\n❌ Debug failed - check errors above")
