#!/usr/bin/env python3
"""
Complete face recognition fix - ensure registration and recognition use same system
"""

import sys
import os
import numpy as np
import cv2
import base64
import json

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def complete_diagnostic():
    """Complete diagnostic of the face recognition pipeline"""
    print("🔍 COMPLETE FACE RECOGNITION DIAGNOSTIC")
    print("=" * 60)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Step 1: Check what's in database
        print("📂 DATABASE CHECK")
        print("-" * 20)
        db = SessionLocal()
        users = db.query(models.User).all()
        users_with_encodings = [u for u in users if u.face_encoding]
        
        print(f"Total users: {len(users)}")
        print(f"Users with encodings: {len(users_with_encodings)}")
        
        if len(users_with_encodings) == 0:
            print("❌ CRITICAL: No users have face encodings!")
            return False
        
        for user in users_with_encodings:
            encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
            print(f"  👤 {user.name} (ID: {user.id})")
            print(f"     Encoding shape: {encoding.shape}")
            print(f"     Encoding norm: {np.linalg.norm(encoding):.6f}")
            print(f"     Encoding mean: {np.mean(encoding):.6f}")
            print(f"     Encoding std: {np.std(encoding):.6f}")
            print(f"     Encoding range: [{np.min(encoding):.3f}, {np.max(encoding):.3f}]")
        
        # Step 2: Test the recognition system directly
        print(f"\n🧠 RECOGNITION SYSTEM TEST")
        print("-" * 30)
        
        # Load faces
        users_data = []
        for user in users_with_encodings:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_system.load_known_faces(users_data)
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            print(f"MediaPipe system loaded encodings: {len(mp_system.known_encodings)}")
            print(f"MediaPipe system names: {mp_system.known_names}")
            print(f"MediaPipe system threshold: {mp_system.similarity_threshold}")
            
            # Test self-similarity of loaded encodings
            if len(mp_system.known_encodings) > 0:
                for i, (name, encoding) in enumerate(zip(mp_system.known_names, mp_system.known_encodings)):
                    self_sim = np.dot(encoding, encoding)
                    print(f"  {name} loaded encoding self-similarity: {self_sim:.6f}")
                    
                    # Compare with database encoding
                    db_user = next((u for u in users_with_encodings if u.name == name), None)
                    if db_user:
                        db_encoding = np.frombuffer(db_user.face_encoding, dtype=np.float32)
                        cross_sim = np.dot(encoding, db_encoding)
                        print(f"  {name} cross-similarity (loaded vs DB): {cross_sim:.6f}")
        
        # Step 3: Test with live camera
        print(f"\n🎥 LIVE CAMERA COMPATIBILITY TEST")
        print("-" * 40)
        print("This will test if live camera embeddings are compatible with stored ones")
        print("Position the registered person's face and press SPACE to test")
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open camera!")
            return False
        
        test_results = []
        
        while len(test_results) < 3:
            ret, frame = cap.read()
            if not ret:
                continue
            
            display_frame = frame.copy()
            cv2.putText(display_frame, f"Test {len(test_results)+1}/3", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(display_frame, "Position registered person's face", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press SPACE to test", (10, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press Q to quit", (10, 130), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            cv2.imshow('Compatibility Test', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):
                print(f"\n📸 Test {len(test_results)+1}: Analyzing frame...")
                
                try:
                    # Test with MediaPipe system
                    if hasattr(face_recognition_system, 'mediapipe_system'):
                        mp_system = face_recognition_system.mediapipe_system
                        results = mp_system.recognize_faces_in_frame(frame)
                        
                        print(f"   Faces detected: {len(results)}")
                        
                        for result in results:
                            name = result.get('name', 'Unknown')
                            confidence = result.get('confidence', 0.0)
                            is_known = result.get('is_known', False)
                            
                            print(f"   Result: {name}, confidence: {confidence:.3f}, known: {is_known}")
                            
                            test_results.append({
                                'name': name,
                                'confidence': confidence,
                                'is_known': is_known
                            })
                            
                            # If we got similarities, show them
                            if 'similarities' in result:
                                print(f"   Similarities: {result['similarities']}")
                
                except Exception as e:
                    print(f"   ❌ Test failed: {e}")
                    import traceback
                    traceback.print_exc()
        
        cap.release()
        cv2.destroyAllWindows()
        
        # Analyze test results
        print(f"\n📊 TEST RESULTS ANALYSIS")
        print("-" * 30)
        
        if len(test_results) == 0:
            print("❌ No test results obtained")
            return False
        
        known_results = [r for r in test_results if r['is_known']]
        unknown_results = [r for r in test_results if not r['is_known']]
        
        print(f"Known results: {len(known_results)}")
        print(f"Unknown results: {len(unknown_results)}")
        
        if len(known_results) > 0:
            avg_confidence = np.mean([r['confidence'] for r in known_results])
            print(f"Average confidence for known: {avg_confidence:.3f}")
        
        if len(unknown_results) > 0:
            avg_confidence = np.mean([r['confidence'] for r in unknown_results])
            print(f"Average confidence for unknown: {avg_confidence:.3f}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_registration_compatibility():
    """Fix registration to use the same system as recognition"""
    print(f"\n🔧 FIXING REGISTRATION COMPATIBILITY")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        print("This will re-register users using the SAME system that recognition uses")
        print("This ensures 100% compatibility between registration and recognition")
        print()
        
        # Get all users
        db = SessionLocal()
        users = db.query(models.User).all()
        
        if len(users) == 0:
            print("❌ No users found!")
            return False
        
        print(f"Found {len(users)} users:")
        for user in users:
            has_encoding = "✅" if user.face_encoding else "❌"
            print(f"  {has_encoding} {user.name} (ID: {user.id})")
        
        print()
        user_to_fix = input("Enter the name of the user to re-register (or 'all' for all users): ")
        
        if user_to_fix.lower() == 'all':
            users_to_fix = users
        else:
            users_to_fix = [u for u in users if u.name.lower() == user_to_fix.lower()]
            if len(users_to_fix) == 0:
                print(f"❌ User '{user_to_fix}' not found!")
                return False
        
        print(f"\nWill re-register {len(users_to_fix)} user(s)")
        
        for user in users_to_fix:
            print(f"\n👤 Re-registering {user.name}...")
            
            # Capture face
            print("🎥 Starting camera for face capture...")
            print("Position the person's face clearly and press SPACE to capture")
            
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                print("❌ Cannot open camera!")
                continue
            
            captured = False
            
            while not captured:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                display_frame = frame.copy()
                cv2.putText(display_frame, f"Registering: {user.name}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(display_frame, "Press SPACE to capture", (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(display_frame, "Press Q to skip", (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                cv2.imshow(f'Register {user.name}', display_frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print(f"   Skipped {user.name}")
                    break
                elif key == ord(' '):
                    print("📸 Capturing and processing...")
                    
                    try:
                        # Use the SAME MediaPipe system for registration
                        if hasattr(face_recognition_system, 'mediapipe_system'):
                            mp_system = face_recognition_system.mediapipe_system
                            
                            # Detect faces
                            results = mp_system.face_detection.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                            
                            if results.detections:
                                detection = results.detections[0]  # Use first face
                                
                                # Extract face region
                                h, w, _ = frame.shape
                                bbox = detection.location_data.relative_bounding_box
                                x = int(bbox.xmin * w)
                                y = int(bbox.ymin * h)
                                width = int(bbox.width * w)
                                height = int(bbox.height * h)
                                
                                # Ensure valid crop
                                x = max(0, x)
                                y = max(0, y)
                                x2 = min(w, x + width)
                                y2 = min(h, y + height)
                                
                                face_crop = frame[y:y2, x:x2]
                                
                                if face_crop.size > 0:
                                    # Extract embedding using the SAME system
                                    embedding = mp_system.extract_face_embedding_openvino(face_crop)
                                    
                                    if embedding is not None and len(embedding) == 128:
                                        # Save to database
                                        user.face_encoding = embedding.astype(np.float32).tobytes()
                                        db.commit()
                                        
                                        print(f"✅ Successfully registered {user.name}")
                                        print(f"   Embedding shape: {embedding.shape}")
                                        print(f"   Embedding norm: {np.linalg.norm(embedding):.3f}")
                                        
                                        captured = True
                                    else:
                                        print("❌ Failed to extract embedding")
                                else:
                                    print("❌ Invalid face crop")
                            else:
                                print("❌ No face detected")
                    
                    except Exception as e:
                        print(f"❌ Registration failed: {e}")
            
            cap.release()
            cv2.destroyAllWindows()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Complete Face Recognition Fix")
    print("This will diagnose and fix face recognition accuracy issues")
    print("by ensuring registration and recognition use the same system")
    print()
    
    # Run diagnostic
    if complete_diagnostic():
        print("\n" + "="*60)
        
        fix_confirm = input("Fix registration compatibility? (y/n): ")
        if fix_confirm.lower() == 'y':
            if fix_registration_compatibility():
                print("\n✅ Registration fix completed!")
                print("Please test the camera recognition now.")
                print("The system should now be accurate.")
            else:
                print("\n❌ Registration fix failed.")
        else:
            print("Fix cancelled.")
    else:
        print("\n❌ Diagnostic failed.")
        print("Please check the errors above and try again.")
