#!/usr/bin/env python3
"""
Diagnose and fix face recognition accuracy issues
"""

import sys
import os
import numpy as np
import cv2

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def diagnose_recognition_issue():
    """Diagnose why recognition is not working accurately"""
    print("🔍 FACE RECOGNITION ACCURACY DIAGNOSIS")
    print("=" * 60)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Step 1: Check database
        print("📂 STEP 1: DATABASE CHECK")
        print("-" * 30)
        db = SessionLocal()
        users = db.query(models.User).all()
        users_with_encodings = [u for u in users if u.face_encoding]
        
        print(f"Total users: {len(users)}")
        print(f"Users with encodings: {len(users_with_encodings)}")
        
        if len(users_with_encodings) == 0:
            print("❌ CRITICAL: No users have face encodings!")
            print("   Solution: Re-register users through web interface")
            return False
        
        for user in users_with_encodings:
            encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
            print(f"  👤 {user.name} (ID: {user.id})")
            print(f"     Encoding: {len(encoding)}D, norm: {np.linalg.norm(encoding):.3f}")
        
        # Step 2: Test face recognition system
        print(f"\n🧠 STEP 2: FACE RECOGNITION SYSTEM TEST")
        print("-" * 45)
        
        # Load faces into system
        users_data = []
        for user in users_with_encodings:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        print(f"Loading {len(users_data)} users into recognition system...")
        face_recognition_system.load_known_faces(users_data)
        
        print(f"After loading:")
        print(f"  Known encodings: {len(face_recognition_system.known_encodings)}")
        print(f"  Known names: {face_recognition_system.known_names}")
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            print(f"  MP encodings: {len(mp_system.known_encodings)}")
            print(f"  MP names: {mp_system.known_names}")
            print(f"  MP threshold: {mp_system.similarity_threshold}")
        
        # Step 3: Test with live camera
        print(f"\n🎥 STEP 3: LIVE CAMERA TEST")
        print("-" * 30)
        print("This will test recognition with live camera feed...")
        print("Press SPACE to test recognition, 'q' to quit")
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open camera!")
            return False
        
        test_count = 0
        max_tests = 5
        
        while test_count < max_tests:
            ret, frame = cap.read()
            if not ret:
                continue
            
            # Show frame
            display_frame = frame.copy()
            cv2.putText(display_frame, f"Test {test_count+1}/{max_tests}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press SPACE to test recognition", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(display_frame, "Press Q to quit", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imshow('Recognition Test', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Space to test
                print(f"\n📸 Test {test_count + 1}: Testing recognition...")
                
                try:
                    # Test with MediaPipe system
                    if hasattr(face_recognition_system, 'mediapipe_system'):
                        mp_system = face_recognition_system.mediapipe_system
                        results = mp_system.recognize_faces_in_frame(frame)
                        
                        print(f"   Faces detected: {len(results)}")
                        
                        for i, result in enumerate(results):
                            print(f"   Face {i+1}:")
                            print(f"     Name: {result.get('name', 'Unknown')}")
                            print(f"     Confidence: {result.get('confidence', 0.0):.3f}")
                            print(f"     Is known: {result.get('is_known', False)}")
                            
                            if 'similarities' in result:
                                print(f"     Similarities: {result['similarities']}")
                    
                    test_count += 1
                    
                except Exception as e:
                    print(f"   ❌ Recognition test failed: {e}")
                    import traceback
                    traceback.print_exc()
        
        cap.release()
        cv2.destroyAllWindows()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_recognition_system():
    """Fix the recognition system for accurate detection"""
    print(f"\n🔧 FIXING RECOGNITION SYSTEM")
    print("=" * 40)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Step 1: Clear and reload all faces
        print("🔄 Step 1: Clearing and reloading faces...")
        
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.face_encoding.isnot(None)).all()
        
        if len(users) == 0:
            print("❌ No users with encodings found!")
            print("   Please re-register users through the web interface")
            return False
        
        # Clear existing faces
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            mp_system.known_encodings = []
            mp_system.known_names = []
            print("✅ Cleared existing faces from recognition system")
        
        # Reload faces
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_system.load_known_faces(users_data)
        print(f"✅ Reloaded {len(users_data)} faces")
        
        # Step 2: Verify loading
        print("\n🔍 Step 2: Verifying face loading...")
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            print(f"   Loaded encodings: {len(mp_system.known_encodings)}")
            print(f"   Loaded names: {mp_system.known_names}")
            print(f"   Threshold: {mp_system.similarity_threshold}")
            
            # Test self-similarity
            if len(mp_system.known_encodings) > 0:
                for i, (name, encoding) in enumerate(zip(mp_system.known_names, mp_system.known_encodings)):
                    self_sim = np.dot(encoding, encoding)
                    print(f"   {name} self-similarity: {self_sim:.3f}")
                    
                    if self_sim < 0.8:
                        print(f"   ⚠️ {name} has low self-similarity - may need re-registration")
        
        # Step 3: Adjust threshold for accuracy
        print("\n⚙️ Step 3: Adjusting threshold for accuracy...")
        
        if hasattr(face_recognition_system, 'mediapipe_system'):
            mp_system = face_recognition_system.mediapipe_system
            # Set a balanced threshold: high enough to avoid false positives, 
            # low enough to recognize registered users
            mp_system.similarity_threshold = 0.5
            print(f"✅ Set threshold to {mp_system.similarity_threshold}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_recommendations():
    """Provide recommendations based on diagnosis"""
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 30)
    print("Based on the diagnosis, here are the recommended actions:")
    print()
    print("1. 🔄 RE-REGISTER USERS:")
    print("   - Go to: http://localhost:8000/users")
    print("   - Delete existing users with face encodings")
    print("   - Re-register them with clear, well-lit photos")
    print("   - Ensure only one face per registration")
    print()
    print("2. 🎯 OPTIMAL CONDITIONS:")
    print("   - Good lighting (avoid shadows)")
    print("   - Face directly facing camera")
    print("   - Clear, unobstructed view")
    print("   - Consistent distance from camera")
    print()
    print("3. 🔧 SYSTEM SETTINGS:")
    print("   - Threshold: 0.5 (balanced accuracy)")
    print("   - Use MediaPipe + MobileFaceNet")
    print("   - Ensure face loading works properly")
    print()
    print("4. 🧪 TESTING:")
    print("   - Test with registered person first")
    print("   - Then test with unknown person")
    print("   - Verify accuracy before production use")

if __name__ == "__main__":
    print("🎯 Face Recognition Accuracy Diagnosis & Fix")
    print("This will diagnose and fix recognition accuracy issues")
    print()
    
    # Run diagnosis
    if diagnose_recognition_issue():
        print("\n" + "="*60)
        
        # Ask if user wants to apply fixes
        fix_confirm = input("Apply automatic fixes? (y/n): ")
        if fix_confirm.lower() == 'y':
            if fix_recognition_system():
                print("\n✅ Fixes applied successfully!")
                print("Please test the camera recognition now.")
            else:
                print("\n❌ Some fixes failed. Check the output above.")
        
        # Provide recommendations
        provide_recommendations()
        
    else:
        print("\n❌ Diagnosis failed. Check the errors above.")
        provide_recommendations()
