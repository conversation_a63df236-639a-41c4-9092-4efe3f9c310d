#!/usr/bin/env python3
"""
Check camera configurations and test RTSP connections
"""

import sys
import os
import cv2

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def check_cameras():
    """Check all cameras in database"""
    print("📹 Camera Configuration Check")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        db = SessionLocal()
        cameras = db.query(models.Camera).all()
        
        print(f"📊 Found {len(cameras)} cameras in database:")
        print()
        
        for camera in cameras:
            print(f"🎥 Camera ID: {camera.id}")
            print(f"   Name: {camera.name}")
            print(f"   Type: {camera.type}")
            print(f"   URL: {camera.url}")
            print(f"   Status: {'Active' if camera.is_active else 'Inactive'}")

            # Test the camera connection
            if camera.is_active:
                print(f"   🔍 Testing connection...")
                test_camera_connection(camera.url, camera.type)
            else:
                print(f"   ⏸️ Camera inactive - skipping test")
            print()
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_camera_connection(url: str, camera_type: str):
    """Test individual camera connection"""
    try:
        # Parse camera source
        if url.isdigit():
            camera_source = int(url)
            print(f"      📱 Webcam index: {camera_source}")
        else:
            camera_source = url
            print(f"      🌐 URL: {camera_source}")
        
        # Test connection
        if isinstance(camera_source, str) and camera_source.startswith(('rtsp://', 'http://', 'https://')):
            print(f"      🔍 Testing RTSP connection with FFMPEG...")
            cap = cv2.VideoCapture(camera_source, cv2.CAP_FFMPEG)
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        else:
            print(f"      🔍 Testing webcam connection...")
            cap = cv2.VideoCapture(camera_source)
        
        if cap.isOpened():
            print(f"      ✅ Connection successful")
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"      ✅ Frame read successful - {frame.shape[1]}x{frame.shape[0]}")
            else:
                print(f"      ❌ Cannot read frames")
            cap.release()
        else:
            print(f"      ❌ Cannot open camera")
            cap.release()
            
    except Exception as e:
        print(f"      ❌ Test failed: {e}")

def check_users():
    """Check users with face encodings"""
    print("👥 User Face Encodings Check")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        import numpy as np
        
        db = SessionLocal()
        users = db.query(models.User).all()
        
        print(f"📊 Found {len(users)} users in database:")
        print()
        
        users_with_encodings = 0
        for user in users:
            print(f"👤 User ID: {user.id}")
            print(f"   Name: {user.name}")
            print(f"   Email: {user.email}")
            print(f"   Active: {'Yes' if user.is_active else 'No'}")
            
            if user.face_encoding:
                encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
                print(f"   Face Encoding: ✅ {len(encoding)}D")
                print(f"   Norm: {np.linalg.norm(encoding):.3f}")
                users_with_encodings += 1
            else:
                print(f"   Face Encoding: ❌ None")
            print()
        
        print(f"📊 Summary: {users_with_encodings}/{len(users)} users have face encodings")
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 System Diagnostics")
    print("This will check camera configurations and user encodings")
    print()
    
    # Check cameras
    check_cameras()
    print()
    
    # Check users
    check_users()
    
    print("=" * 60)
    print("✅ Diagnostics complete!")
    print()
    print("If RTSP cameras fail:")
    print("1. Check if the RTSP URL is correct")
    print("2. Verify network connectivity")
    print("3. Check camera credentials")
    print("4. Try different RTSP parameters")
    print()
    print("If face recognition fails:")
    print("1. Ensure users have face encodings")
    print("2. Check if faces are being loaded into recognition system")
    print("3. Verify similarity threshold settings")
