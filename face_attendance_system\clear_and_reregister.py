#!/usr/bin/env python3
"""
Clear all old encodings and force fresh MobileFaceNet registration
This script removes ALL face encodings to ensure pure MobileFaceNet compatibility
"""

import sys
import os

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def clear_all_encodings():
    """Clear ALL face encodings from database"""
    print("🧹 Clear All Face Encodings for Pure MobileFaceNet")
    print("=" * 60)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        db = SessionLocal()
        users = db.query(models.User).all()
        
        print(f"Found {len(users)} users in database:")
        
        # Show current status
        for user in users:
            if user.face_encoding:
                try:
                    import numpy as np
                    encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
                    encoding_type = f"{len(encoding)}D"
                    if len(encoding) == 128:
                        encoding_type += " (MobileFaceNet compatible)"
                    elif len(encoding) == 512:
                        encoding_type += " (ArcFace - incompatible)"
                    else:
                        encoding_type += " (Unknown format)"
                    print(f"  ✅ {user.name}: {encoding_type}")
                except:
                    print(f"  ❌ {user.name}: Corrupted encoding")
            else:
                print(f"  ⚪ {user.name}: No encoding")
        
        print(f"\n⚠️  WARNING: This will DELETE ALL face encodings!")
        print("This ensures 100% compatibility with the new MobileFaceNet system.")
        print("All users will need to re-register their faces.")
        print("\nBenefits:")
        print("  ✅ No more negative similarity scores")
        print("  ✅ Accurate face recognition")
        print("  ✅ Proper 'Unknown' detection for strangers")
        print("  ✅ Clean MobileFaceNet-only system")
        
        confirm = input(f"\nType 'CLEAR ALL' to proceed: ")
        if confirm != 'CLEAR ALL':
            print("❌ Operation cancelled.")
            return False
        
        # Clear all encodings
        cleared_count = 0
        for user in users:
            if user.face_encoding:
                user.face_encoding = None
                cleared_count += 1
        
        db.commit()
        
        print(f"\n✅ Successfully cleared {cleared_count} face encodings")
        print("\n🎯 System is now ready for pure MobileFaceNet!")
        
        print("\n📋 Next Steps:")
        print("1. Restart the application")
        print("2. Go to 'Register User' page")
        print("3. Register each user with their face")
        print("4. New encodings will be 128D MobileFaceNet format")
        print("5. Face recognition will work accurately!")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_system_status():
    """Show current system and encoding status"""
    print("📊 Current System Status")
    print("=" * 40)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        import numpy as np
        
        db = SessionLocal()
        users = db.query(models.User).all()
        
        mobilefacenet_count = 0
        arcface_count = 0
        other_count = 0
        no_encoding_count = 0
        
        for user in users:
            if user.face_encoding:
                try:
                    encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
                    
                    if len(encoding) == 128:
                        mobilefacenet_count += 1
                        print(f"  ✅ {user.name}: MobileFaceNet (128D)")
                    elif len(encoding) == 512:
                        arcface_count += 1
                        print(f"  ⚠️ {user.name}: ArcFace (512D) - INCOMPATIBLE")
                    else:
                        other_count += 1
                        print(f"  ❓ {user.name}: Unknown ({len(encoding)}D)")
                except:
                    other_count += 1
                    print(f"  ❌ {user.name}: Corrupted encoding")
            else:
                no_encoding_count += 1
                print(f"  ⚪ {user.name}: No encoding")
        
        print(f"\n📈 Summary:")
        print(f"  MobileFaceNet (compatible): {mobilefacenet_count}")
        print(f"  ArcFace (incompatible): {arcface_count}")
        print(f"  Other/Corrupted: {other_count}")
        print(f"  No encoding: {no_encoding_count}")
        
        total_incompatible = arcface_count + other_count
        if total_incompatible > 0:
            print(f"\n⚠️  {total_incompatible} users have incompatible encodings")
            print("These are causing negative similarity scores and recognition failures.")
            print("Clearing all encodings is recommended for best results.")
        
        if mobilefacenet_count > 0:
            print(f"\n✅ {mobilefacenet_count} users already have MobileFaceNet encodings")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")

if __name__ == "__main__":
    print("🎯 MobileFaceNet System Cleanup Tool")
    print("This tool fixes negative similarity scores and recognition issues")
    print()
    
    # Show current status
    show_system_status()
    
    print("\n" + "=" * 60)
    print("The issue you're experiencing:")
    print("  ❌ Negative similarity scores (-0.621)")
    print("  ❌ All faces showing as 'Unknown'")
    print("  ❌ ArcFace encodings incompatible with MobileFaceNet")
    print("\nSolution:")
    print("  ✅ Clear all old encodings")
    print("  ✅ Fresh MobileFaceNet registration")
    print("  ✅ Positive similarity scores")
    print("  ✅ Accurate recognition")
    
    print("\nOptions:")
    print("1. Clear all encodings and start fresh (RECOMMENDED)")
    print("2. Exit without changes")
    
    choice = input("\nEnter your choice (1-2): ")
    
    if choice == "1":
        if clear_all_encodings():
            print("\n🎉 System cleaned! Ready for MobileFaceNet registration.")
        else:
            print("\n❌ Cleanup failed.")
    else:
        print("Exited without changes.")
        print("Note: Recognition issues will persist until encodings are cleared.")
