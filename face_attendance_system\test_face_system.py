#!/usr/bin/env python3
"""
Test script to verify the MobileFaceNet system is working
"""

import sys
import os

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_face_system():
    """Test the face recognition system"""
    print("🧪 Testing MobileFaceNet Face Recognition System")
    print("=" * 60)
    
    try:
        # Import the face recognition system
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        print("✅ Successfully imported face recognition system")
        
        # Test system initialization
        print(f"📊 System info:")
        print(f"   Using MediaPipe: {face_recognition_system.use_mediapipe}")
        print(f"   Using InsightFace: {face_recognition_system.use_insightface}")
        print(f"   Similarity threshold: {face_recognition_system.similarity_threshold}")
        
        # Load faces from database
        print("\n📂 Loading faces from database...")
        db = SessionLocal()
        users = db.query(models.User).all()
        
        users_data = []
        for user in users:
            if user.face_encoding:
                users_data.append({
                    'id': user.id,
                    'name': user.name,
                    'face_encoding': user.face_encoding
                })
                print(f"   Found user: {user.name}")
        
        print(f"📊 Found {len(users_data)} users with face encodings")
        
        # Load faces into system
        if len(users_data) > 0:
            print("\n🔄 Loading faces into recognition system...")
            face_recognition_system.load_known_faces(users_data)
            
            print(f"✅ Loaded faces:")
            print(f"   Known encodings: {len(face_recognition_system.known_encodings)}")
            print(f"   Known names: {face_recognition_system.known_names}")
            print(f"   Known user IDs: {face_recognition_system.known_user_ids}")
            
            if len(face_recognition_system.known_encodings) > 0:
                print("\n✅ Face recognition system is ready!")
                print("The system should now be able to recognize faces.")
            else:
                print("\n⚠️ No faces loaded into recognition system")
                print("This could be due to incompatible encoding formats.")
        else:
            print("\n⚠️ No users with face encodings found in database")
            print("Users need to register their faces first.")
        
        db.close()
        
        # Test MediaPipe system specifically
        if face_recognition_system.use_mediapipe:
            print("\n🎯 Testing MediaPipe + MobileFaceNet system...")
            mediapipe_system = face_recognition_system.mediapipe_system
            print(f"   MediaPipe system available: {mediapipe_system is not None}")
            if mediapipe_system:
                print(f"   Similarity threshold: {mediapipe_system.similarity_threshold}")
                print(f"   Known encodings: {len(mediapipe_system.known_encodings)}")
                print(f"   Known names: {mediapipe_system.known_names}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing face system: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Face Recognition System Test")
    print("This will test if the MobileFaceNet system is working correctly")
    print()
    
    success = test_face_system()
    
    if success:
        print("\n🎉 Face recognition system test completed!")
        print("Check the output above for any issues.")
    else:
        print("\n❌ Face recognition system test failed!")
        print("Check the error messages above.")
    
    print("\nNext steps:")
    print("1. Start the web application")
    print("2. Go to the dashboard")
    print("3. Start a camera to test face recognition")
    print("4. Check the terminal logs for recognition results")
